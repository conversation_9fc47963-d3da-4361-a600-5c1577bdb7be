/* eslint-disable @typescript-eslint/no-explicit-any,react-hooks/exhaustive-deps */

'use client';

import { Moon, Sun } from 'lucide-react';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

export function ThemeToggle() {
  const [mounted, setMounted] = useState(false);
  const { setTheme, resolvedTheme } = useTheme();

  const setThemeColor = (theme?: string) => {
    const meta = document.querySelector('meta[name="theme-color"]');
    if (!meta) {
      const meta = document.createElement('meta');
      meta.name = 'theme-color';
      meta.content = '#000000'; // 固定为黑色
      document.head.appendChild(meta);
    } else {
      meta.setAttribute('content', '#000000'); // 固定为黑色
    }
  };

  useEffect(() => {
    setMounted(true);
    setTheme('dark'); // 强制设置为暗色主题
    setThemeColor('dark');
  }, [setTheme]);

  // 隐藏主题切换按钮，返回空div
  return <div className='w-10 h-10' style={{ display: 'none' }} />;
}
