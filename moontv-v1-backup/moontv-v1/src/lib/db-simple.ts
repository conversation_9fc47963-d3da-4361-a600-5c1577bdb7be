/* eslint-disable no-console,@typescript-eslint/no-explicit-any */

import { AdminConfig } from './admin.types';
import { D1Storage } from './d1.db';
import { RedisStorage } from './redis.db';
import { Favorite, IStorage, PlayRecord, SkipConfig } from './types';
import { UpstashRedisStorage } from './upstash.db';

// storage type 常量: 'localstorage' | 'redis' | 'd1' | 'upstash'，默认 'localstorage'
const STORAGE_TYPE =
  (process.env.NEXT_PUBLIC_STORAGE_TYPE as
    | 'localstorage'
    | 'redis'
    | 'd1'
    | 'upstash'
    | undefined) || 'localstorage';

// 创建存储实例
async function createStorage(): Promise<IStorage> {
  switch (STORAGE_TYPE) {
    case 'redis':
      return new RedisStorage();
    case 'upstash':
      return new UpstashRedisStorage();
    case 'd1':
      return new D1Storage();

    case 'localstorage':
    default:
      // 默认返回内存实现，保证本地开发可用
      return null as unknown as IStorage;
  }
}

// 单例存储实例
let storageInstance: IStorage | null = null;

async function getStorage(): Promise<IStorage> {
  if (!storageInstance) {
    storageInstance = await createStorage();
  }
  return storageInstance;
}

// 数据库操作接口
export const db = {
  // 用户相关
  async checkUserExist(userName: string): Promise<boolean> {
    const storage = await getStorage();
    return storage.checkUserExist(userName);
  },

  async verifyUser(userName: string, password: string): Promise<boolean> {
    const storage = await getStorage();
    return storage.verifyUser(userName, password);
  },

  async getUserRole(userName: string): Promise<string> {
    const storage = await getStorage();
    // 如果存储支持getUserRole方法，使用它；否则返回默认角色
    if (storage && typeof (storage as any).getUserRole === 'function') {
      return (storage as any).getUserRole(userName);
    }
    return 'user'; // 默认角色
  },

  // 播放记录相关
  async getPlayRecords(userName: string): Promise<PlayRecord[]> {
    const storage = await getStorage();
    const allRecords = await storage.getAllPlayRecords(userName);
    return Object.values(allRecords);
  },

  async addPlayRecord(userName: string, record: PlayRecord): Promise<void> {
    const storage = await getStorage();
    const key = `${record.source_name || 'default'}+${record.search_title}`;
    return storage.setPlayRecord(userName, key, record);
  },

  async deletePlayRecord(
    userName: string,
    source: string,
    id: string
  ): Promise<void> {
    const storage = await getStorage();
    const key = `${source}+${id}`;
    return storage.deletePlayRecord(userName, key);
  },

  async clearPlayRecords(userName: string): Promise<void> {
    const storage = await getStorage();
    // 获取所有记录然后逐个删除
    const allRecords = await storage.getAllPlayRecords(userName);
    for (const key of Object.keys(allRecords)) {
      await storage.deletePlayRecord(userName, key);
    }
  },

  // 收藏相关
  async getFavorites(userName: string): Promise<Favorite[]> {
    const storage = await getStorage();
    const allFavorites = await storage.getAllFavorites(userName);
    return Object.values(allFavorites);
  },

  async addFavorite(userName: string, favorite: Favorite): Promise<void> {
    const storage = await getStorage();
    const key = `${favorite.source_name || 'default'}+${favorite.search_title}`;
    return storage.setFavorite(userName, key, favorite);
  },

  async deleteFavorite(
    userName: string,
    source: string,
    id: string
  ): Promise<void> {
    const storage = await getStorage();
    const key = `${source}+${id}`;
    return storage.deleteFavorite(userName, key);
  },

  async clearFavorites(userName: string): Promise<void> {
    const storage = await getStorage();
    // 获取所有收藏然后逐个删除
    const allFavorites = await storage.getAllFavorites(userName);
    for (const key of Object.keys(allFavorites)) {
      await storage.deleteFavorite(userName, key);
    }
  },

  // 跳过配置相关
  async getSkipConfig(
    userName: string,
    source: string,
    id: string
  ): Promise<SkipConfig | null> {
    const storage = await getStorage();
    return storage.getSkipConfig(userName, source, id);
  },

  async setSkipConfig(
    userName: string,
    source: string,
    id: string,
    config: SkipConfig
  ): Promise<void> {
    const storage = await getStorage();
    return storage.setSkipConfig(userName, source, id, config);
  },

  async deleteSkipConfig(
    userName: string,
    source: string,
    id: string
  ): Promise<void> {
    const storage = await getStorage();
    return storage.deleteSkipConfig(userName, source, id);
  },

  async getAllSkipConfigs(
    userName: string
  ): Promise<{ [key: string]: SkipConfig }> {
    const storage = await getStorage();
    return storage.getAllSkipConfigs(userName);
  },

  // 管理员配置相关
  async getAdminConfig(): Promise<AdminConfig | null> {
    const storage = await getStorage();
    return storage.getAdminConfig();
  },

  async setAdminConfig(config: AdminConfig): Promise<void> {
    const storage = await getStorage();
    return storage.setAdminConfig(config);
  },
};
