// 新的存储接口类型定义，用于API存储

export interface WatchRecord {
  id: string;
  videoId: string;
  title: string;
  url: string;
  currentTime: number;
  duration: number;
  progress: number;
  lastWatched: string;
}

export interface Favorite {
  id: string;
  videoId: string;
  title: string;
  url: string;
  pic?: string;
  type?: string;
  year?: string;
  area?: string;
  director?: string;
  actor?: string;
  des?: string;
  addedAt: string;
}

export interface SkipConfig {
  videoId: string;
  skipStart: number;
  skipEnd: number;
}

export interface AdminConfig {
  siteName: string;
  password: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  display_name: string;
  vip_level: number;
  role: string;
}

// 新的存储接口
export interface StorageInterface {
  // 认证相关
  login(credentials: { username: string; password: string }): Promise<boolean>;
  logout(): Promise<void>;
  verifyAuth(): Promise<boolean>;
  getCurrentUser(): User | null;
  isAuthenticated(): boolean;

  // 观看记录
  getWatchRecords(): Promise<WatchRecord[]>;
  saveWatchRecord(record: Omit<WatchRecord, 'id' | 'lastWatched'>): Promise<void>;
  getWatchRecord(videoId: string): Promise<WatchRecord | null>;
  deleteWatchRecord(videoId: string): Promise<void>;

  // 收藏功能
  getFavorites(): Promise<Favorite[]>;
  addFavorite(favorite: Omit<Favorite, 'id' | 'addedAt'>): Promise<void>;
  removeFavorite(videoId: string): Promise<void>;
  isFavorite(videoId: string): Promise<boolean>;

  // 跳过配置
  getSkipConfig(videoId: string): Promise<SkipConfig | null>;
  saveSkipConfig(config: SkipConfig): Promise<void>;

  // 管理员配置
  getAdminConfig(): Promise<AdminConfig>;
  saveAdminConfig(config: AdminConfig): Promise<void>;

  // 清理数据
  clearAllData(): Promise<void>;
}
