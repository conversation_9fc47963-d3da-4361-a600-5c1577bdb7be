import { StorageInterface, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>onfig, AdminConfig } from './types';

interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  token?: string;
  user?: any;
}

interface LoginCredentials {
  username: string;
  password: string;
}

export class ApiStorage implements StorageInterface {
  private baseUrl: string;
  private token: string | null = null;
  private user: any = null;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://47.111.120.88:3001';
    // 从localStorage恢复token
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('moontv_token');
      const userData = localStorage.getItem('moontv_user');
      if (userData) {
        try {
          this.user = JSON.parse(userData);
        } catch (e) {
          console.error('解析用户数据失败:', e);
        }
      }
    }
  }

  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      (headers as any)['Authorization'] = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API请求失败 ${endpoint}:`, error);
      throw error;
    }
  }

  // 认证相关方法
  async login(credentials: LoginCredentials): Promise<boolean> {
    try {
      const response = await this.request<{ token: string; user: any }>('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify(credentials),
      });

      if (response.success && response.token) {
        this.token = response.token;
        this.user = response.user;
        
        // 保存到localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('moontv_token', this.token);
          localStorage.setItem('moontv_user', JSON.stringify(this.user));
        }
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('登录失败:', error);
      return false;
    }
  }

  async logout(): Promise<void> {
    this.token = null;
    this.user = null;
    
    if (typeof window !== 'undefined') {
      localStorage.removeItem('moontv_token');
      localStorage.removeItem('moontv_user');
    }
  }

  async verifyAuth(): Promise<boolean> {
    if (!this.token) return false;

    try {
      const response = await this.request('/api/auth/verify');
      if (response.success) {
        this.user = response.user;
        if (typeof window !== 'undefined') {
          localStorage.setItem('moontv_user', JSON.stringify(this.user));
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('验证认证失败:', error);
      // 清除无效token
      await this.logout();
      return false;
    }
  }

  getCurrentUser(): any {
    return this.user;
  }

  isAuthenticated(): boolean {
    return !!this.token && !!this.user;
  }

  // 观看记录
  async getWatchRecords(): Promise<WatchRecord[]> {
    try {
      const response = await this.request<WatchRecord[]>('/api/watch-records');
      return response.data || [];
    } catch (error) {
      console.error('获取观看记录失败:', error);
      return [];
    }
  }

  async saveWatchRecord(record: Omit<WatchRecord, 'id' | 'lastWatched'>): Promise<void> {
    try {
      await this.request('/api/watch-records', {
        method: 'POST',
        body: JSON.stringify({
          video_id: record.videoId,
          video_title: record.title,
          video_url: record.url,
          current_time: record.currentTime,
          duration: record.duration,
          progress: record.progress,
        }),
      });
    } catch (error) {
      console.error('保存观看记录失败:', error);
      throw error;
    }
  }

  async getWatchRecord(videoId: string): Promise<WatchRecord | null> {
    try {
      const records = await this.getWatchRecords();
      return records.find(record => record.videoId === videoId) || null;
    } catch (error) {
      console.error('获取观看记录失败:', error);
      return null;
    }
  }

  async deleteWatchRecord(videoId: string): Promise<void> {
    // API暂未实现删除单个记录，可以后续添加
    console.warn('删除观看记录功能暂未实现');
  }

  // 收藏功能
  async getFavorites(): Promise<Favorite[]> {
    try {
      const response = await this.request<Favorite[]>('/api/favorites');
      return response.data || [];
    } catch (error) {
      console.error('获取收藏列表失败:', error);
      return [];
    }
  }

  async addFavorite(favorite: Omit<Favorite, 'id' | 'addedAt'>): Promise<void> {
    try {
      await this.request('/api/favorites', {
        method: 'POST',
        body: JSON.stringify({
          video_id: favorite.videoId,
          video_title: favorite.title,
          video_url: favorite.url,
          video_pic: favorite.pic,
          video_type: favorite.type,
          video_year: favorite.year,
          video_area: favorite.area,
          video_director: favorite.director,
          video_actor: favorite.actor,
          video_des: favorite.des,
        }),
      });
    } catch (error) {
      console.error('添加收藏失败:', error);
      throw error;
    }
  }

  async removeFavorite(videoId: string): Promise<void> {
    try {
      await this.request(`/api/favorites/${videoId}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error('移除收藏失败:', error);
      throw error;
    }
  }

  async isFavorite(videoId: string): Promise<boolean> {
    try {
      const favorites = await this.getFavorites();
      return favorites.some(fav => fav.videoId === videoId);
    } catch (error) {
      console.error('检查收藏状态失败:', error);
      return false;
    }
  }

  // 跳过配置（暂时使用localStorage实现）
  async getSkipConfig(videoId: string): Promise<SkipConfig | null> {
    if (typeof window === 'undefined') return null;
    
    try {
      const configs = JSON.parse(localStorage.getItem('moontv_skip_configs') || '{}');
      return configs[videoId] || null;
    } catch (error) {
      console.error('获取跳过配置失败:', error);
      return null;
    }
  }

  async saveSkipConfig(config: SkipConfig): Promise<void> {
    if (typeof window === 'undefined') return;
    
    try {
      const configs = JSON.parse(localStorage.getItem('moontv_skip_configs') || '{}');
      configs[config.videoId] = config;
      localStorage.setItem('moontv_skip_configs', JSON.stringify(configs));
    } catch (error) {
      console.error('保存跳过配置失败:', error);
      throw error;
    }
  }

  // 管理员配置（暂时使用localStorage实现）
  async getAdminConfig(): Promise<AdminConfig> {
    if (typeof window === 'undefined') {
      return {
        siteName: process.env.SITE_NAME || '电玩管家视频站',
        password: process.env.PASSWORD || '123456',
      };
    }
    
    try {
      const config = JSON.parse(localStorage.getItem('moontv_admin_config') || '{}');
      return {
        siteName: config.siteName || process.env.SITE_NAME || '电玩管家视频站',
        password: config.password || process.env.PASSWORD || '123456',
      };
    } catch (error) {
      console.error('获取管理员配置失败:', error);
      return {
        siteName: process.env.SITE_NAME || '电玩管家视频站',
        password: process.env.PASSWORD || '123456',
      };
    }
  }

  async saveAdminConfig(config: AdminConfig): Promise<void> {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem('moontv_admin_config', JSON.stringify(config));
    } catch (error) {
      console.error('保存管理员配置失败:', error);
      throw error;
    }
  }

  // 清理数据
  async clearAllData(): Promise<void> {
    if (typeof window === 'undefined') return;
    
    localStorage.removeItem('moontv_skip_configs');
    localStorage.removeItem('moontv_admin_config');
    await this.logout();
  }
}
