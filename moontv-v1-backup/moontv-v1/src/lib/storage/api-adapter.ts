import { AdminConfig } from '../admin.types';
import { Favorite, IStorage, PlayRecord, SkipConfig } from '../types';
import { ApiStorage } from './api-storage';
import { AdminConfig as ApiAdminConfig, SkipConfig as ApiSkipConfig } from './types';

// API存储适配器，将新的API存储接口适配到现有的IStorage接口
export class ApiStorageAdapter implements IStorage {
  private apiStorage: ApiStorage;
  private currentUser = 'default';

  constructor() {
    this.apiStorage = new ApiStorage();
  }

  // 设置当前用户
  setCurrentUser(userName: string) {
    this.currentUser = userName;
  }

  // 将API存储的WatchRecord转换为PlayRecord
  private convertToPlayRecord(apiRecord: any): PlayRecord {
    return {
      title: apiRecord.video_title || apiRecord.title,
      source_name: 'api',
      cover: '',
      year: '',
      index: 1,
      total_episodes: 1,
      play_time: apiRecord.current_time_seconds || apiRecord.currentTime || 0,
      total_time: apiRecord.duration_seconds || apiRecord.duration || 0,
      save_time: new Date(apiRecord.last_watched || apiRecord.lastWatched).getTime(),
      search_title: apiRecord.video_title || apiRecord.title,
    };
  }

  // 将PlayRecord转换为API存储格式
  private convertFromPlayRecord(record: PlayRecord, videoId: string): any {
    return {
      videoId,
      title: record.title,
      url: '',
      currentTime: record.play_time,
      duration: record.total_time,
      progress: record.total_time > 0 ? (record.play_time / record.total_time) * 100 : 0,
    };
  }

  // 将API存储的Favorite转换为现有格式
  private convertToFavorite(apiFavorite: any): Favorite {
    return {
      source_name: 'api',
      total_episodes: 1,
      title: apiFavorite.video_title || apiFavorite.title,
      year: apiFavorite.video_year || apiFavorite.year || '',
      cover: apiFavorite.video_pic || apiFavorite.pic || '',
      save_time: new Date(apiFavorite.created_at || apiFavorite.addedAt).getTime(),
      search_title: apiFavorite.video_title || apiFavorite.title,
    };
  }

  // 将现有Favorite转换为API存储格式
  private convertFromFavorite(favorite: Favorite, videoId: string): any {
    return {
      videoId,
      title: favorite.title,
      url: '',
      pic: favorite.cover,
      type: '',
      year: favorite.year,
      area: '',
      director: '',
      actor: '',
      des: '',
    };
  }

  // ---------- 播放记录 ----------
  async getPlayRecord(userName: string, key: string): Promise<PlayRecord | null> {
    try {
      const videoId = key.split('+')[1] || key;
      const apiRecord = await this.apiStorage.getWatchRecord(videoId);
      return apiRecord ? this.convertToPlayRecord(apiRecord) : null;
    } catch (error) {
      console.error('获取播放记录失败:', error);
      return null;
    }
  }

  async setPlayRecord(userName: string, key: string, record: PlayRecord): Promise<void> {
    try {
      const videoId = key.split('+')[1] || key;
      const apiRecord = this.convertFromPlayRecord(record, videoId);
      await this.apiStorage.saveWatchRecord(apiRecord);
    } catch (error) {
      console.error('保存播放记录失败:', error);
      throw error;
    }
  }

  async getAllPlayRecords(userName: string): Promise<{ [key: string]: PlayRecord }> {
    try {
      const apiRecords = await this.apiStorage.getWatchRecords();
      const result: { [key: string]: PlayRecord } = {};
      
      apiRecords.forEach((apiRecord: any) => {
        const key = `api+${apiRecord.video_id || apiRecord.videoId}`;
        result[key] = this.convertToPlayRecord(apiRecord);
      });
      
      return result;
    } catch (error) {
      console.error('获取所有播放记录失败:', error);
      return {};
    }
  }

  async deletePlayRecord(userName: string, key: string): Promise<void> {
    try {
      const videoId = key.split('+')[1] || key;
      await this.apiStorage.deleteWatchRecord(videoId);
    } catch (error) {
      console.error('删除播放记录失败:', error);
      throw error;
    }
  }

  // ---------- 收藏 ----------
  async getFavorite(userName: string, key: string): Promise<Favorite | null> {
    try {
      const videoId = key.split('+')[1] || key;
      const isFav = await this.apiStorage.isFavorite(videoId);
      if (!isFav) return null;
      
      const favorites = await this.apiStorage.getFavorites();
      const apiFavorite = favorites.find((fav: any) => (fav.video_id || fav.videoId) === videoId);
      return apiFavorite ? this.convertToFavorite(apiFavorite) : null;
    } catch (error) {
      console.error('获取收藏失败:', error);
      return null;
    }
  }

  async setFavorite(userName: string, key: string, favorite: Favorite): Promise<void> {
    try {
      const videoId = key.split('+')[1] || key;
      const apiFavorite = this.convertFromFavorite(favorite, videoId);
      await this.apiStorage.addFavorite(apiFavorite);
    } catch (error) {
      console.error('保存收藏失败:', error);
      throw error;
    }
  }

  async getAllFavorites(userName: string): Promise<{ [key: string]: Favorite }> {
    try {
      const apiFavorites = await this.apiStorage.getFavorites();
      const result: { [key: string]: Favorite } = {};
      
      apiFavorites.forEach((apiFavorite: any) => {
        const key = `api+${apiFavorite.video_id || apiFavorite.videoId}`;
        result[key] = this.convertToFavorite(apiFavorite);
      });
      
      return result;
    } catch (error) {
      console.error('获取所有收藏失败:', error);
      return {};
    }
  }

  async deleteFavorite(userName: string, key: string): Promise<void> {
    try {
      const videoId = key.split('+')[1] || key;
      await this.apiStorage.removeFavorite(videoId);
    } catch (error) {
      console.error('删除收藏失败:', error);
      throw error;
    }
  }

  // ---------- 用户相关 ----------
  async registerUser(userName: string, password: string): Promise<void> {
    // API存储不支持注册，抛出错误
    throw new Error('API存储不支持用户注册');
  }

  async verifyUser(userName: string, password: string): Promise<boolean> {
    try {
      const success = await this.apiStorage.login({ username: userName, password });
      if (success) {
        this.currentUser = userName;
      }
      return success;
    } catch (error) {
      console.error('用户验证失败:', error);
      return false;
    }
  }

  async checkUserExist(userName: string): Promise<boolean> {
    // 简单实现：尝试验证认证状态
    return this.apiStorage.isAuthenticated();
  }

  async changePassword(userName: string, newPassword: string): Promise<void> {
    throw new Error('API存储不支持修改密码');
  }

  async deleteUser(userName: string): Promise<void> {
    await this.apiStorage.clearAllData();
  }

  // ---------- 搜索历史 ----------
  async getSearchHistory(userName: string): Promise<string[]> {
    // 使用localStorage实现
    if (typeof window === 'undefined') return [];
    
    try {
      const history = JSON.parse(localStorage.getItem('moontv_search_history') || '[]');
      return Array.isArray(history) ? history : [];
    } catch (error) {
      console.error('获取搜索历史失败:', error);
      return [];
    }
  }

  async addSearchHistory(userName: string, keyword: string): Promise<void> {
    if (typeof window === 'undefined') return;
    
    try {
      const history = await this.getSearchHistory(userName);
      const newHistory = [keyword, ...history.filter(h => h !== keyword)].slice(0, 20);
      localStorage.setItem('moontv_search_history', JSON.stringify(newHistory));
    } catch (error) {
      console.error('添加搜索历史失败:', error);
    }
  }

  async deleteSearchHistory(userName: string, keyword?: string): Promise<void> {
    if (typeof window === 'undefined') return;
    
    try {
      if (keyword) {
        const history = await this.getSearchHistory(userName);
        const newHistory = history.filter(h => h !== keyword);
        localStorage.setItem('moontv_search_history', JSON.stringify(newHistory));
      } else {
        localStorage.removeItem('moontv_search_history');
      }
    } catch (error) {
      console.error('删除搜索历史失败:', error);
    }
  }

  // ---------- 用户列表 ----------
  async getAllUsers(): Promise<string[]> {
    const user = this.apiStorage.getCurrentUser();
    return user ? [user.username] : [];
  }

  // ---------- 管理员配置 ----------
  async getAdminConfig(): Promise<AdminConfig | null> {
    try {
      const apiConfig = await this.apiStorage.getAdminConfig();
      if (!apiConfig) return null;

      // 转换API配置到IStorage配置格式
      return {
        SiteConfig: {
          SiteName: apiConfig.siteName,
          Announcement: '',
          SearchDownstreamMaxPage: 10,
          SiteInterfaceCacheTime: 1800,
          ImageProxy: '',
          DoubanProxy: '',
          DisableYellowFilter: false,
        },
        UserConfig: {
          AllowRegister: false,
          Users: [],
        },
        SourceConfig: [],
        CustomCategories: [],
      };
    } catch (error) {
      console.error('获取管理员配置失败:', error);
      return null;
    }
  }

  async setAdminConfig(config: AdminConfig): Promise<void> {
    try {
      // 转换IStorage配置到API配置格式
      const apiConfig: ApiAdminConfig = {
        siteName: config.SiteConfig.SiteName,
        password: '123456', // 使用默认密码
      };
      await this.apiStorage.saveAdminConfig(apiConfig);
    } catch (error) {
      console.error('保存管理员配置失败:', error);
      throw error;
    }
  }

  // ---------- 跳过配置 ----------
  async getSkipConfig(userName: string, source: string, id: string): Promise<SkipConfig | null> {
    try {
      const videoId = `${source}+${id}`;
      const apiConfig = await this.apiStorage.getSkipConfig(videoId);
      if (!apiConfig) return null;

      // 转换API配置到IStorage配置格式
      return {
        enable: true,
        intro_time: apiConfig.skipStart,
        outro_time: apiConfig.skipEnd,
      };
    } catch (error) {
      console.error('获取跳过配置失败:', error);
      return null;
    }
  }

  async setSkipConfig(userName: string, source: string, id: string, config: SkipConfig): Promise<void> {
    try {
      const videoId = `${source}+${id}`;
      // 转换IStorage配置到API配置格式
      const apiConfig: ApiSkipConfig = {
        videoId,
        skipStart: config.intro_time,
        skipEnd: config.outro_time,
      };
      await this.apiStorage.saveSkipConfig(apiConfig);
    } catch (error) {
      console.error('保存跳过配置失败:', error);
      throw error;
    }
  }

  async deleteSkipConfig(userName: string, source: string, id: string): Promise<void> {
    // API存储暂不支持删除跳过配置
    console.warn('API存储暂不支持删除跳过配置');
  }

  async getAllSkipConfigs(userName: string): Promise<{ [key: string]: SkipConfig }> {
    // API存储暂不支持获取所有跳过配置
    return {};
  }
}
