const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { pool, testConnection, verifyWordPressPassword, getUserById } = require('./database');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;
const JWT_SECRET = process.env.JWT_SECRET || 'moontv-jwt-secret-key-2025';

// CORS配置
const allowedOrigins = (process.env.ALLOWED_ORIGINS || 'http://localhost:3000').split(',');
app.use(cors({
  origin: allowedOrigins,
  credentials: true
}));

app.use(express.json());

// 内存存储（作为数据库的后备方案）
const users = new Map();
const watchRecords = new Map();
const favorites = new Map();
const skipConfigs = new Map();
const adminConfig = new Map();

// 初始化内存用户（后备方案）
async function initMemoryUsers() {
  const hashedPassword = await bcrypt.hash('password', 10);
  
  users.set('admin', {
    id: 1,
    username: 'admin',
    password: hashedPassword,
    email: '<EMAIL>',
    display_name: '管理员',
    role: 'administrator',
    vip_level: 3,
    membership_level: 'permanent'
  });
  
  users.set('testuser', {
    id: 2,
    username: 'testuser',
    password: hashedPassword,
    email: '<EMAIL>',
    display_name: '测试用户',
    role: 'subscriber',
    vip_level: 1,
    membership_level: 'month'
  });
}

// 初始化内存用户
initMemoryUsers();

// JWT认证中间件
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: '缺少访问令牌' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ success: false, message: '令牌无效或已过期' });
    }
    req.user = user;
    next();
  });
}

// 管理员权限中间件
function requireAdmin(req, res, next) {
  if (req.user.role !== 'administrator') {
    return res.status(403).json({ success: false, message: '需要管理员权限' });
  }
  next();
}

// API路由

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'MoonTV API服务运行正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 用户登录
app.post('/api/login', async (req, res) => {
app.post('/api/login', async (req, res) => {
  // 重定向到 /api/login
  const response = await fetch('http://localhost:3001/api/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(req.body)
  });
  const data = await response.json();
  res.status(response.status).json(data);
});
  try {
    const { username, email } = req.body;

    if (!username || !email) {
      return res.status(400).json({
        success: false,
        message: '用户名和邮箱不能为空'
      });
    }

    // 首先尝试数据库验证
    let user = await verifyWordPressUser(username, email);
    
    // 如果数据库验证失败，尝试内存用户（后备方案）
    if (!user) {
      const memoryUser = users.get(username);
      if (memoryUser) {
        const isValidEmail = memoryUser.email === email;
        if (isValidEmail) {
          user = memoryUser;
          console.log('使用内存用户验证成功:', username);
        }
      }
    } else {
      console.log('数据库用户验证成功:', username, '会员等级:', user.membership_level);
    }

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或邮箱错误'
      });
    }

    // 检查用户状态
    if (user.status && user.status !== 0) {
      return res.status(401).json({
        success: false,
        message: '用户账号已被禁用'
      });
    }

    const token = jwt.sign(
      {
        id: user.id,
        username: user.username,
        role: user.role,
        vip_level: user.vip_level,
        membership_level: user.membership_level
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.json({
      success: true,
      message: '登录成功',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        display_name: user.display_name,
        vip_level: user.vip_level,
        role: user.role,
        membership_level: user.membership_level || 'free'
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 验证令牌
app.get('/api/auth/verify', authenticateToken, (req, res) => {
  const user = users.get(req.user.username);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: '用户不存在'
    });
  }

  res.json({
    success: true,
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      display_name: user.display_name,
      role: user.role,
      vip_level: user.vip_level
    }
  });
});

// 观看记录相关API
app.get('/api/watch-records', authenticateToken, (req, res) => {
  const userRecords = Array.from(watchRecords.values())
    .filter(record => record.user_id === req.user.id)
    .sort((a, b) => new Date(b.watch_date) - new Date(a.watch_date));

  res.json({
    success: true,
    data: userRecords
  });
});

app.post('/api/watch-records', authenticateToken, (req, res) => {
  const { video_id, video_title, current_time, duration } = req.body;
  
  if (!video_id) {
    return res.status(400).json({
      success: false,
      message: '视频ID不能为空'
    });
  }

  const recordKey = `${req.user.id}_${video_id}`;
  const record = {
    id: recordKey,
    user_id: req.user.id,
    video_id,
    video_title: video_title || '',
    current_time: current_time || 0,
    duration: duration || 0,
    watch_date: new Date().toISOString()
  };

  watchRecords.set(recordKey, record);

  res.json({
    success: true,
    message: '观看记录保存成功',
    data: record
  });
});

// 收藏相关API
app.get('/api/favorites', authenticateToken, (req, res) => {
  const userFavorites = Array.from(favorites.values())
    .filter(fav => fav.user_id === req.user.id)
    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

  res.json({
    success: true,
    data: userFavorites
  });
});

app.post('/api/favorites', authenticateToken, (req, res) => {
  const { video_id, video_title, video_pic, video_url } = req.body;
  
  if (!video_id) {
    return res.status(400).json({
      success: false,
      message: '视频ID不能为空'
    });
  }

  const favoriteKey = `${req.user.id}_${video_id}`;
  
  if (favorites.has(favoriteKey)) {
    return res.status(400).json({
      success: false,
      message: '该视频已在收藏列表中'
    });
  }

  const favorite = {
    id: favoriteKey,
    user_id: req.user.id,
    video_id,
    video_title: video_title || '',
    video_pic: video_pic || '',
    video_url: video_url || '',
    created_at: new Date().toISOString()
  };

  favorites.set(favoriteKey, favorite);

  res.json({
    success: true,
    message: '收藏成功',
    data: favorite
  });
});

app.delete('/api/favorites/:video_id', authenticateToken, (req, res) => {
  const { video_id } = req.params;
  const favoriteKey = `${req.user.id}_${video_id}`;
  
  if (!favorites.has(favoriteKey)) {
    return res.status(404).json({
      success: false,
      message: '收藏记录不存在'
    });
  }

  favorites.delete(favoriteKey);

  res.json({
    success: true,
    message: '取消收藏成功'
  });
});

// 启动服务器
app.listen(PORT, '0.0.0.0', async () => {
  console.log('🚀 MoonTV API服务器启动成功');
  console.log(`📍 服务地址: http://0.0.0.0:${PORT}`);
  console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
  console.log(`📊 允许的来源: ${process.env.ALLOWED_ORIGINS || 'http://localhost:3000'}`);
  
  // 测试数据库连接
  console.log('🔍 正在测试数据库连接...');
  const dbConnected = await testConnection();
  if (dbConnected) {
    console.log('✅ 数据库连接正常，将使用WordPress用户验证');
  } else {
    console.log('⚠️  数据库连接失败，将使用内存用户验证');
  }
});
