const { pool } = require('./database');

// 观看记录相关操作
class WatchRecordService {
  // 获取用户观看记录
  static async getUserWatchRecords(userId, limit = 50) {
    try {
      const connection = await pool.getConnection();
      const [records] = await connection.execute(
        'SELECT * FROM moontv_watch_records WHERE user_id = ? ORDER BY updated_at DESC LIMIT ?',
        [userId, limit]
      );
      connection.release();
      return records;
    } catch (error) {
      console.error('获取观看记录失败:', error);
      return [];
    }
  }

  // 保存观看记录
  static async saveWatchRecord(userId, videoId, recordData) {
    try {
      const connection = await pool.getConnection();
      
      const { videoName, videoPic, currentTime, duration, progress, sourceKey } = recordData;
      
      await connection.execute(
        `INSERT INTO moontv_watch_records 
         (user_id, video_id, video_name, video_pic, current_time_seconds, duration_seconds, progress_percent, source_key)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         video_name = VALUES(video_name),
         video_pic = VALUES(video_pic),
         current_time_seconds = VALUES(current_time_seconds),
         duration_seconds = VALUES(duration_seconds),
         progress_percent = VALUES(progress_percent),
         source_key = VALUES(source_key),
         updated_at = CURRENT_TIMESTAMP`,
        [userId, videoId, videoName, videoPic, currentTime, duration, progress, sourceKey]
      );
      
      connection.release();
      return true;
    } catch (error) {
      console.error('保存观看记录失败:', error);
      return false;
    }
  }

  // 删除观看记录
  static async deleteWatchRecord(userId, videoId) {
    try {
      const connection = await pool.getConnection();
      await connection.execute(
        'DELETE FROM moontv_watch_records WHERE user_id = ? AND video_id = ?',
        [userId, videoId]
      );
      connection.release();
      return true;
    } catch (error) {
      console.error('删除观看记录失败:', error);
      return false;
    }
  }
}

// 收藏相关操作
class FavoriteService {
  // 获取用户收藏
  static async getUserFavorites(userId, limit = 100) {
    try {
      const connection = await pool.getConnection();
      const [favorites] = await connection.execute(
        'SELECT * FROM moontv_favorites WHERE user_id = ? ORDER BY created_at DESC LIMIT ?',
        [userId, limit]
      );
      connection.release();
      
      // 解析JSON数据
      return favorites.map(fav => ({
        ...fav,
        video_data: typeof fav.video_data === 'string' ? JSON.parse(fav.video_data) : fav.video_data
      }));
    } catch (error) {
      console.error('获取收藏失败:', error);
      return [];
    }
  }

  // 添加收藏
  static async addFavorite(userId, videoId, videoData) {
    try {
      const connection = await pool.getConnection();
      await connection.execute(
        'INSERT INTO moontv_favorites (user_id, video_id, video_data) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE video_data = VALUES(video_data)',
        [userId, videoId, JSON.stringify(videoData)]
      );
      connection.release();
      return true;
    } catch (error) {
      console.error('添加收藏失败:', error);
      return false;
    }
  }

  // 删除收藏
  static async removeFavorite(userId, videoId) {
    try {
      const connection = await pool.getConnection();
      await connection.execute(
        'DELETE FROM moontv_favorites WHERE user_id = ? AND video_id = ?',
        [userId, videoId]
      );
      connection.release();
      return true;
    } catch (error) {
      console.error('删除收藏失败:', error);
      return false;
    }
  }

  // 检查是否已收藏
  static async isFavorited(userId, videoId) {
    try {
      const connection = await pool.getConnection();
      const [result] = await connection.execute(
        'SELECT COUNT(*) as count FROM moontv_favorites WHERE user_id = ? AND video_id = ?',
        [userId, videoId]
      );
      connection.release();
      return result[0].count > 0;
    } catch (error) {
      console.error('检查收藏状态失败:', error);
      return false;
    }
  }
}

// 跳过配置相关操作
class SkipConfigService {
  // 获取跳过配置
  static async getSkipConfig(userId, videoId) {
    try {
      const connection = await pool.getConnection();
      const [configs] = await connection.execute(
        'SELECT * FROM moontv_skip_configs WHERE user_id = ? AND video_id = ?',
        [userId, videoId]
      );
      connection.release();
      return configs.length > 0 ? configs[0] : null;
    } catch (error) {
      console.error('获取跳过配置失败:', error);
      return null;
    }
  }

  // 保存跳过配置
  static async saveSkipConfig(userId, videoId, skipStart, skipEnd) {
    try {
      const connection = await pool.getConnection();
      await connection.execute(
        `INSERT INTO moontv_skip_configs (user_id, video_id, skip_start, skip_end)
         VALUES (?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         skip_start = VALUES(skip_start),
         skip_end = VALUES(skip_end),
         updated_at = CURRENT_TIMESTAMP`,
        [userId, videoId, skipStart, skipEnd]
      );
      connection.release();
      return true;
    } catch (error) {
      console.error('保存跳过配置失败:', error);
      return false;
    }
  }
}

// 管理员配置相关操作
class AdminConfigService {
  // 获取管理员配置
  static async getAdminConfig() {
    try {
      const connection = await pool.getConnection();
      const [configs] = await connection.execute(
        'SELECT config_value FROM moontv_admin_config WHERE config_key = ?',
        ['admin_config']
      );
      connection.release();
      
      if (configs.length > 0) {
        return typeof configs[0].config_value === 'string' 
          ? JSON.parse(configs[0].config_value) 
          : configs[0].config_value;
      }
      return null;
    } catch (error) {
      console.error('获取管理员配置失败:', error);
      return null;
    }
  }

  // 保存管理员配置
  static async setAdminConfig(config) {
    try {
      const connection = await pool.getConnection();
      await connection.execute(
        `INSERT INTO moontv_admin_config (config_key, config_value)
         VALUES (?, ?)
         ON DUPLICATE KEY UPDATE
         config_value = VALUES(config_value),
         updated_at = CURRENT_TIMESTAMP`,
        ['admin_config', JSON.stringify(config)]
      );
      connection.release();
      return true;
    } catch (error) {
      console.error('保存管理员配置失败:', error);
      return false;
    }
  }

  // 获取所有用户名
  static async getAllUsers() {
    try {
      const connection = await pool.getConnection();
      const [users] = await connection.execute(
        'SELECT user_login FROM wp_users ORDER BY ID'
      );
      connection.release();
      return users.map(user => user.user_login);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      return [];
    }
  }
}

module.exports = {
  WatchRecordService,
  FavoriteService,
  SkipConfigService,
  AdminConfigService
};
