{"name": "moontv-api", "version": "1.0.0", "description": "MoonTV独立API服务 - WordPress用户认证和数据存储", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["moontv", "api", "wordpress", "mysql", "express"], "author": "MoonTV Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.11.5"}}