const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { pool } = require('./database');

// WordPress密码验证（兼容PHPass）
function wpCheckPassword(password, hash) {
  // WordPress使用PHPass格式的密码哈希
  if (hash.startsWith('$P$') || hash.startsWith('$H$')) {
    // 简化的PHPass验证，实际应该使用完整的PHPass库
    // 这里使用bcrypt作为fallback
    try {
      const bcryptHash = hash.replace(/^\$[PH]\$/, '$2a$');
      return bcrypt.compareSync(password, bcryptHash);
    } catch (e) {
      return false;
    }
  }
  
  // MD5 fallback（旧版WordPress）
  const crypto = require('crypto');
  const md5Hash = crypto.createHash('md5').update(password).digest('hex');
  return md5Hash === hash;
}

// 验证WordPress用户
async function verifyWordPressUser(username, password) {
  try {
    const connection = await pool.getConnection();
    
    // 查询用户信息
    const [users] = await connection.execute(
      'SELECT ID, user_login, user_pass, user_email, user_nicename, display_name FROM wp_users WHERE user_login = ? OR user_email = ?',
      [username, username]
    );
    
    if (users.length === 0) {
      connection.release();
      return null;
    }
    
    const user = users[0];
    
    // 验证密码
    if (!wpCheckPassword(password, user.user_pass)) {
      connection.release();
      return null;
    }
    
    // 获取用户角色和权限
    const [userMeta] = await connection.execute(
      'SELECT meta_value FROM wp_usermeta WHERE user_id = ? AND meta_key = ?',
      [user.ID, 'wp_capabilities']
    );
    
    let capabilities = {};
    let role = 'subscriber';
    
    if (userMeta.length > 0) {
      try {
        capabilities = JSON.parse(userMeta[0].meta_value) || {};
        // 获取用户角色
        const roles = Object.keys(capabilities);
        if (roles.length > 0) {
          role = roles[0];
        }
      } catch (e) {
        console.error('解析用户权限失败:', e);
      }
    }
    
    // 获取会员等级（如果使用RiPro主题）
    const [memberLevel] = await connection.execute(
      'SELECT meta_value FROM wp_usermeta WHERE user_id = ? AND meta_key = ?',
      [user.ID, 'user_vip_level']
    );
    
    let vipLevel = 0;
    if (memberLevel.length > 0) {
      vipLevel = parseInt(memberLevel[0].meta_value) || 0;
    }
    
    connection.release();
    
    return {
      id: user.ID,
      username: user.user_login,
      email: user.user_email,
      nicename: user.user_nicename,
      displayName: user.display_name,
      role: role,
      capabilities: capabilities,
      vipLevel: vipLevel
    };
    
  } catch (error) {
    console.error('WordPress用户验证失败:', error);
    return null;
  }
}

// 生成JWT Token
function generateToken(user) {
  const payload = {
    id: user.id,
    username: user.username,
    email: user.email,
    role: user.role,
    vipLevel: user.vipLevel
  };
  
  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: '7d' // 7天过期
  });
}

// 验证JWT Token
function verifyToken(token) {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    return null;
  }
}

// 检查用户权限
function checkPermission(user, requiredRole = 'subscriber') {
  const roleHierarchy = {
    'subscriber': 0,
    'contributor': 1,
    'author': 2,
    'editor': 3,
    'administrator': 4
  };
  
  const userLevel = roleHierarchy[user.role] || 0;
  const requiredLevel = roleHierarchy[requiredRole] || 0;
  
  return userLevel >= requiredLevel;
}

// 获取会员权限
function getMembershipPermissions(vipLevel) {
  const permissions = {
    0: { // 免费用户
      maxWatchTime: 30 * 60, // 30分钟
      maxDailyWatch: 3, // 每天3个视频
      canDownload: false,
      canSkipAd: false
    },
    1: { // VIP1
      maxWatchTime: 2 * 60 * 60, // 2小时
      maxDailyWatch: 10,
      canDownload: false,
      canSkipAd: true
    },
    2: { // VIP2
      maxWatchTime: -1, // 无限制
      maxDailyWatch: -1,
      canDownload: true,
      canSkipAd: true
    }
  };
  
  return permissions[vipLevel] || permissions[0];
}

module.exports = {
  verifyWordPressUser,
  generateToken,
  verifyToken,
  checkPermission,
  getMembershipPermissions
};
