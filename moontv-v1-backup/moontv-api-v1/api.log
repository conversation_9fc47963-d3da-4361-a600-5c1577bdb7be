nohup: ignoring input
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: reconnect. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
[dotenv@17.2.1] injecting env (9) from .env -- tip: 🔐 prevent building .env in docker: https://dotenvx.com/prebuild
🚀 MoonTV API服务器启动成功
📍 服务地址: http://0.0.0.0:3001
🌍 环境: production
📊 允许的来源: http://*************:3000,http://localhost:3000
🔍 正在测试数据库连接...
✅ 数据库连接成功
✅ 数据库连接正常，将使用WordPress用户验证
检测到WordPress PHPass哈希，暂时跳过验证
数据库用户验证成功: admin 会员等级: free
