const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || '47_111_120_88_40',
  password: process.env.DB_PASSWORD || 'ndRfwdpxDx',
  database: process.env.DB_NAME || '47_111_120_88_40',
  charset: 'utf8mb4',
  connectionLimit: 10,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 测试数据库连接
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    console.log('✅ 数据库连接成功');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return false;
  }
}

// WordPress用户验证（用户名+邮箱）
async function verifyWordPressUser(username, email) {
  try {
    const connection = await pool.getConnection();
    
    // 查询用户信息：用户名和邮箱必须同时匹配
    const [userRows] = await connection.execute(
      'SELECT ID, user_login, user_email, user_nicename, user_status FROM wp_users WHERE user_login = ? AND user_email = ? AND user_status = 0',
      [username, email]
    );
    
    if (userRows.length === 0) {
      connection.release();
      console.log(`❌ 用户验证失败: 用户名 "${username}" 和邮箱 "${email}" 不匹配或用户不存在`);
      return null;
    }
    
    const user = userRows[0];
    console.log(`✅ 用户验证成功: ${user.user_login} (${user.user_email})`);
    
    // 获取用户元数据（会员等级等）
    const [metaRows] = await connection.execute(
      'SELECT meta_key, meta_value FROM wp_usermeta WHERE user_id = ? AND meta_key IN (?, ?, ?, ?)',
      [user.ID, 'membership_level', 'vip_level', 'user_role', 'wp_capabilities']
    );
    
    // 处理用户元数据
    const userMeta = {};
    metaRows.forEach(row => {
      userMeta[row.meta_key] = row.meta_value;
    });
    
    // 解析WordPress角色
    let role = 'subscriber';
    let membershipLevel = 'free';
    let vipLevel = 0;
    
    if (userMeta.wp_capabilities) {
      try {
        const capabilities = JSON.parse(userMeta.wp_capabilities);
        if (capabilities.administrator) role = 'administrator';
        else if (capabilities.editor) role = 'editor';
        else if (capabilities.author) role = 'author';
        else if (capabilities.contributor) role = 'contributor';
      } catch (e) {
        // 如果解析失败，保持默认值
      }
    }
    
    if (userMeta.membership_level) {
      membershipLevel = userMeta.membership_level;
    }
    
    if (userMeta.vip_level) {
      vipLevel = parseInt(userMeta.vip_level) || 0;
    }
    
    connection.release();
    
    return {
      id: user.ID,
      username: user.user_login,
      email: user.user_email,
      display_name: user.user_nicename,
      role: role,
      membership_level: membershipLevel,
      vip_level: vipLevel
    };
  } catch (error) {
    console.error('WordPress用户验证失败:', error);
    return null;
  }
}

module.exports = {
  pool,
  testConnection,
  verifyWordPressUser
};
