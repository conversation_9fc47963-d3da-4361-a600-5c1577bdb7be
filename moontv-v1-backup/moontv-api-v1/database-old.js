const { wpCheckPassword } = require("./wp-password");
const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: process.env.MYSQL_PORT || 3306,
  user: process.env.MYSQL_USER || '47_111_120_88_40',
  password: process.env.MYSQL_PASSWORD || 'ndRfwdpxDx',
  database: process.env.MYSQL_DATABASE || '47_111_120_88_40',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 测试数据库连接
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    console.log('✅ 数据库连接成功');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
}

// 初始化数据库表
async function initializeTables() {
  try {
    const connection = await pool.getConnection();
    
    // 创建MoonTV相关表
    const tables = [
      // 观看记录表
      `CREATE TABLE IF NOT EXISTS moontv_watch_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        video_id VARCHAR(255) NOT NULL,
        video_title VARCHAR(500),
        current_time INT DEFAULT 0,
        duration INT DEFAULT 0,
        watch_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_video (user_id, video_id),
        INDEX idx_watch_date (watch_date)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,
      
      // 收藏表
      `CREATE TABLE IF NOT EXISTS moontv_favorites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        video_id VARCHAR(255) NOT NULL,
        video_title VARCHAR(500),
        video_pic VARCHAR(500),
        video_url VARCHAR(1000),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_video (user_id, video_id),
        INDEX idx_user_id (user_id),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,
      
      // 跳过配置表
      `CREATE TABLE IF NOT EXISTS moontv_skip_configs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        config_data JSON,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user (user_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,
      
      // 管理员配置表
      `CREATE TABLE IF NOT EXISTS moontv_admin_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        config_key VARCHAR(255) NOT NULL,
        config_value TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_key (config_key)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`
    ];
    
    for (const tableSQL of tables) {
      await connection.execute(tableSQL);
    }
    
    connection.release();
    console.log('✅ 数据库表初始化完成');
    return true;
  } catch (error) {
    console.error('❌ 数据库表初始化失败:', error);
    return false;
  }
}

// 验证WordPress用户密码
async function verifyWordPressPassword(username, password) {
  try {
    const connection = await pool.getConnection();
    
    // 查询用户信息
    const [userRows] = await connection.execute(
      'SELECT ID, user_login, user_pass, user_email, user_nicename, user_status FROM wp_users WHERE user_login = ? AND user_status = 0',
      [username]
    );
    
    if (userRows.length === 0) {
      connection.release();
      return null;
    }
    
    const user = userRows[0];
    
    // WordPress密码验证
    const isValidPassword = wpCheckPassword(password, user.user_pass);
    }
    // 2. 检查是否是bcrypt哈希
    else if (user.user_pass.startsWith('$2a$') || user.user_pass.startsWith('$2b$') || user.user_pass.startsWith('$2y$')) {
      const bcrypt = require('bcryptjs');
      isValidPassword = await bcrypt.compare(password, user.user_pass);
    }
    // 3. 检查是否是WordPress PHPass哈希（简化版）
    else if (user.user_pass.startsWith('$P$')) {
      // 这里简化处理，实际应该使用PHPass库
      // 暂时允许通过，您可以根据需要实现完整的PHPass验证
      console.log('检测到WordPress PHPass哈希，暂时跳过验证');
      isValidPassword = true;
    }
    
    if (!isValidPassword) {
      connection.release();
      return null;
    }
    
    // 获取用户元数据（会员等级等）
    const [metaRows] = await connection.execute(
      'SELECT meta_key, meta_value FROM wp_usermeta WHERE user_id = ? AND meta_key IN (?, ?, ?, ?)',
      [user.ID, 'membership_level', 'vip_level', 'user_role', 'wp_capabilities']
    );
    
    connection.release();
    
    // 处理用户元数据
    const userMeta = {};
    metaRows.forEach(row => {
      userMeta[row.meta_key] = row.meta_value;
    });
    
    // 确定会员等级
    let vipLevel = 0;
    let membershipLevel = 'free';
    
    if (userMeta.membership_level) {
      membershipLevel = userMeta.membership_level;
      // RiPro主题的会员等级
      switch (userMeta.membership_level) {
        case 'month': vipLevel = 1; break;
        case 'year': vipLevel = 2; break;
        case 'permanent': vipLevel = 3; break;
        default: vipLevel = 0;
      }
    } else if (userMeta.vip_level) {
      vipLevel = parseInt(userMeta.vip_level) || 0;
    }
    
    // 确定用户角色
    let role = 'subscriber';
    if (userMeta.wp_capabilities) {
      try {
        const capabilities = JSON.parse(userMeta.wp_capabilities) || {};
        if (capabilities.administrator) role = 'administrator';
        else if (capabilities.editor) role = 'editor';
        else if (capabilities.author) role = 'author';
        else if (capabilities.contributor) role = 'contributor';
        else role = 'subscriber';
      } catch (e) {
        // 解析失败，使用默认角色
        role = 'subscriber';
      }
    }
    
    return {
      id: user.ID,
      username: user.user_login,
      email: user.user_email,
      display_name: user.user_nicename || user.user_login,
      vip_level: vipLevel,
      role: role,
      membership_level: membershipLevel,
      status: user.user_status
    };
    
  } catch (error) {
    console.error('数据库用户验证失败:', error);
    return null;
  }
}

// 获取用户信息
async function getUserById(userId) {
  try {
    const connection = await pool.getConnection();
    
    const [userRows] = await connection.execute(
      'SELECT ID, user_login, user_email, user_nicename, user_status FROM wp_users WHERE ID = ? AND user_status = 0',
      [userId]
    );
    
    if (userRows.length === 0) {
      connection.release();
      return null;
    }
    
    const user = userRows[0];
    
    // 获取用户元数据
    const [metaRows] = await connection.execute(
      'SELECT meta_key, meta_value FROM wp_usermeta WHERE user_id = ? AND meta_key IN (?, ?, ?, ?)',
      [user.ID, 'membership_level', 'vip_level', 'user_role', 'wp_capabilities']
    );
    
    connection.release();
    
    // 处理用户元数据
    const userMeta = {};
    metaRows.forEach(row => {
      userMeta[row.meta_key] = row.meta_value;
    });
    
    // 确定会员等级
    let vipLevel = 0;
    let membershipLevel = 'free';
    
    if (userMeta.membership_level) {
      membershipLevel = userMeta.membership_level;
      switch (userMeta.membership_level) {
        case 'month': vipLevel = 1; break;
        case 'year': vipLevel = 2; break;
        case 'permanent': vipLevel = 3; break;
        default: vipLevel = 0;
      }
    } else if (userMeta.vip_level) {
      vipLevel = parseInt(userMeta.vip_level) || 0;
    }
    
    // 确定用户角色
    let role = 'subscriber';
    if (userMeta.wp_capabilities) {
      try {
        const capabilities = JSON.parse(userMeta.wp_capabilities) || {};
        if (capabilities.administrator) role = 'administrator';
        else if (capabilities.editor) role = 'editor';
        else if (capabilities.author) role = 'author';
        else if (capabilities.contributor) role = 'contributor';
        else role = 'subscriber';
      } catch (e) {
        role = 'subscriber';
      }
    }
    
    return {
      id: user.ID,
      username: user.user_login,
      email: user.user_email,
      display_name: user.user_nicename || user.user_login,
      vip_level: vipLevel,
      role: role,
      membership_level: membershipLevel,
      status: user.user_status
    };
    
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
}

module.exports = {
  pool,
  testConnection,
  initializeTables,
  verifyWordPressPassword,
  getUserById
};
