const express = require('express');
const cors = require('cors');
require('dotenv').config();

const { testConnection, initializeTables } = require('./database');
const { 
  verifyWordPressUser, 
  generateToken, 
  verifyToken, 
  checkPermission, 
  getMembershipPermissions 
} = require('./auth');
const {
  WatchRecordService,
  FavoriteService,
  SkipConfigService,
  AdminConfigService
} = require('./storage');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件配置
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// CORS配置
const allowedOrigins = process.env.ALLOWED_ORIGINS ? 
  process.env.ALLOWED_ORIGINS.split(',') : 
  ['http://localhost:3000'];

app.use(cors({
  origin: allowedOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// 认证中间件
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: '缺少认证令牌' });
  }

  const user = verifyToken(token);
  if (!user) {
    return res.status(403).json({ error: '无效的认证令牌' });
  }

  req.user = user;
  next();
}

// 管理员权限中间件
function requireAdmin(req, res, next) {
  if (!checkPermission(req.user, 'administrator')) {
    return res.status(403).json({ error: '需要管理员权限' });
  }
  next();
}

// ==================== 认证相关API ====================

// 用户登录
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: '用户名和密码不能为空' });
    }

    const user = await verifyWordPressUser(username, password);
    if (!user) {
      return res.status(401).json({ error: '用户名或密码错误' });
    }

    const token = generateToken(user);
    const permissions = getMembershipPermissions(user.vipLevel);

    res.json({
      success: true,
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        displayName: user.displayName,
        role: user.role,
        vipLevel: user.vipLevel,
        permissions
      }
    });
  } catch (error) {
    console.error('登录失败:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 验证令牌
app.get('/api/auth/verify', authenticateToken, (req, res) => {
  const permissions = getMembershipPermissions(req.user.vipLevel);
  res.json({
    success: true,
    user: {
      ...req.user,
      permissions
    }
  });
});

// ==================== 观看记录API ====================

// 获取观看记录
app.get('/api/watch-records', authenticateToken, async (req, res) => {
  try {
    const { limit = 50 } = req.query;
    const records = await WatchRecordService.getUserWatchRecords(req.user.id, parseInt(limit));
    res.json({ success: true, data: records });
  } catch (error) {
    console.error('获取观看记录失败:', error);
    res.status(500).json({ error: '获取观看记录失败' });
  }
});

// 保存观看记录
app.post('/api/watch-records', authenticateToken, async (req, res) => {
  try {
    const { videoId, recordData } = req.body;
    
    if (!videoId || !recordData) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const success = await WatchRecordService.saveWatchRecord(req.user.id, videoId, recordData);
    if (success) {
      res.json({ success: true });
    } else {
      res.status(500).json({ error: '保存观看记录失败' });
    }
  } catch (error) {
    console.error('保存观看记录失败:', error);
    res.status(500).json({ error: '保存观看记录失败' });
  }
});

// 删除观看记录
app.delete('/api/watch-records/:videoId', authenticateToken, async (req, res) => {
  try {
    const { videoId } = req.params;
    const success = await WatchRecordService.deleteWatchRecord(req.user.id, videoId);
    if (success) {
      res.json({ success: true });
    } else {
      res.status(500).json({ error: '删除观看记录失败' });
    }
  } catch (error) {
    console.error('删除观看记录失败:', error);
    res.status(500).json({ error: '删除观看记录失败' });
  }
});

// ==================== 收藏API ====================

// 获取收藏列表
app.get('/api/favorites', authenticateToken, async (req, res) => {
  try {
    const { limit = 100 } = req.query;
    const favorites = await FavoriteService.getUserFavorites(req.user.id, parseInt(limit));
    res.json({ success: true, data: favorites });
  } catch (error) {
    console.error('获取收藏失败:', error);
    res.status(500).json({ error: '获取收藏失败' });
  }
});

// 添加收藏
app.post('/api/favorites', authenticateToken, async (req, res) => {
  try {
    const { videoId, videoData } = req.body;
    
    if (!videoId || !videoData) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const success = await FavoriteService.addFavorite(req.user.id, videoId, videoData);
    if (success) {
      res.json({ success: true });
    } else {
      res.status(500).json({ error: '添加收藏失败' });
    }
  } catch (error) {
    console.error('添加收藏失败:', error);
    res.status(500).json({ error: '添加收藏失败' });
  }
});

// 删除收藏
app.delete('/api/favorites/:videoId', authenticateToken, async (req, res) => {
  try {
    const { videoId } = req.params;
    const success = await FavoriteService.removeFavorite(req.user.id, videoId);
    if (success) {
      res.json({ success: true });
    } else {
      res.status(500).json({ error: '删除收藏失败' });
    }
  } catch (error) {
    console.error('删除收藏失败:', error);
    res.status(500).json({ error: '删除收藏失败' });
  }
});

// 检查收藏状态
app.get('/api/favorites/:videoId/status', authenticateToken, async (req, res) => {
  try {
    const { videoId } = req.params;
    const isFavorited = await FavoriteService.isFavorited(req.user.id, videoId);
    res.json({ success: true, isFavorited });
  } catch (error) {
    console.error('检查收藏状态失败:', error);
    res.status(500).json({ error: '检查收藏状态失败' });
  }
});

// ==================== 跳过配置API ====================

// 获取跳过配置
app.get('/api/skip-config/:videoId', authenticateToken, async (req, res) => {
  try {
    const { videoId } = req.params;
    const config = await SkipConfigService.getSkipConfig(req.user.id, videoId);
    res.json({ success: true, data: config });
  } catch (error) {
    console.error('获取跳过配置失败:', error);
    res.status(500).json({ error: '获取跳过配置失败' });
  }
});

// 保存跳过配置
app.post('/api/skip-config', authenticateToken, async (req, res) => {
  try {
    const { videoId, skipStart, skipEnd } = req.body;
    
    if (!videoId || skipStart === undefined || skipEnd === undefined) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const success = await SkipConfigService.saveSkipConfig(req.user.id, videoId, skipStart, skipEnd);
    if (success) {
      res.json({ success: true });
    } else {
      res.status(500).json({ error: '保存跳过配置失败' });
    }
  } catch (error) {
    console.error('保存跳过配置失败:', error);
    res.status(500).json({ error: '保存跳过配置失败' });
  }
});

// ==================== 管理员配置API ====================

// 获取管理员配置
app.get('/api/admin/config', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const config = await AdminConfigService.getAdminConfig();
    res.json({ success: true, data: config });
  } catch (error) {
    console.error('获取管理员配置失败:', error);
    res.status(500).json({ error: '获取管理员配置失败' });
  }
});

// 保存管理员配置
app.post('/api/admin/config', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { config } = req.body;
    
    if (!config) {
      return res.status(400).json({ error: '缺少配置数据' });
    }

    const success = await AdminConfigService.setAdminConfig(config);
    if (success) {
      res.json({ success: true });
    } else {
      res.status(500).json({ error: '保存管理员配置失败' });
    }
  } catch (error) {
    console.error('保存管理员配置失败:', error);
    res.status(500).json({ error: '保存管理员配置失败' });
  }
});

// 获取所有用户
app.get('/api/admin/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const users = await AdminConfigService.getAllUsers();
    res.json({ success: true, data: users });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({ error: '获取用户列表失败' });
  }
});

// ==================== 健康检查API ====================

app.get('/api/health', (req, res) => {
  res.json({ 
    success: true, 
    message: 'MoonTV API服务运行正常',
    timestamp: new Date().toISOString()
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({ error: '服务器内部错误' });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({ error: '接口不存在' });
});

// 启动服务器
async function startServer() {
  try {
    // 测试数据库连接
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ 数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    // 初始化数据库表
    const tablesInitialized = await initializeTables();
    if (!tablesInitialized) {
      console.error('❌ 数据库表初始化失败，服务器启动中止');
      process.exit(1);
    }

    // 启动HTTP服务器
    app.listen(PORT, '0.0.0.0', () => {
      console.log(`🚀 MoonTV API服务器启动成功`);
      console.log(`📍 服务地址: http://0.0.0.0:${PORT}`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📊 允许的来源: ${allowedOrigins.join(', ')}`);
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('�� 收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

// 启动服务器
startServer();
