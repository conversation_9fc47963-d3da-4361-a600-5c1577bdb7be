const { wpCheckPassword } = require('./wp-password');
const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || '47_111_120_88_40',
  password: process.env.DB_PASSWORD || 'ndRfwdpxDx',
  database: process.env.DB_NAME || '47_111_120_88_40',
  charset: 'utf8mb4',
  connectionLimit: 10,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 测试数据库连接
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    console.log('✅ 数据库连接成功');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return false;
  }
}

// 初始化数据库表
async function initializeTables() {
  try {
    const connection = await pool.getConnection();
    
    // 创建MoonTV相关表
    const tables = [
      `CREATE TABLE IF NOT EXISTS moontv_watch_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        video_key VARCHAR(255) NOT NULL,
        title VARCHAR(255) NOT NULL,
        source_name VARCHAR(100) NOT NULL,
        cover TEXT,
        year VARCHAR(10),
        episode_index INT DEFAULT 0,
        total_episodes INT DEFAULT 0,
        play_time INT DEFAULT 0,
        total_time INT DEFAULT 0,
        search_title VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_video (user_id, video_key),
        INDEX idx_user_id (user_id),
        INDEX idx_updated_at (updated_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,
      
      `CREATE TABLE IF NOT EXISTS moontv_favorites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        video_key VARCHAR(255) NOT NULL,
        title VARCHAR(255) NOT NULL,
        source_name VARCHAR(100) NOT NULL,
        cover TEXT,
        year VARCHAR(10),
        search_title VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_favorite (user_id, video_key),
        INDEX idx_user_id (user_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,
      
      `CREATE TABLE IF NOT EXISTS moontv_skip_configs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        video_id VARCHAR(255) NOT NULL,
        skip_start INT DEFAULT 0,
        skip_end INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_video (video_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,
      
      `CREATE TABLE IF NOT EXISTS moontv_admin_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        config_key VARCHAR(100) NOT NULL,
        config_value TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_key (config_key)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`
    ];
    
    for (const tableSQL of tables) {
      await connection.execute(tableSQL);
    }
    
    connection.release();
    console.log('✅ 数据库表初始化完成');
    return true;
  } catch (error) {
    console.error('❌ 数据库表初始化失败:', error);
    return false;
  }
}

// WordPress用户验证
async function verifyWordPressPassword(username, password) {
  try {
    const connection = await pool.getConnection();
    
    // 查询用户信息
    const [userRows] = await connection.execute(
      'SELECT ID, user_login, user_pass, user_email, user_nicename, user_status FROM wp_users WHERE user_login = ? AND user_status = 0',
      [username]
    );
    
    if (userRows.length === 0) {
      connection.release();
      return null;
    }
    
    const user = userRows[0];
    
    // WordPress密码验证
    const isValidPassword = wpCheckPassword(password, user.user_pass);
    
    if (!isValidPassword) {
      connection.release();
      return null;
    }
    
    // 获取用户元数据（会员等级等）
    const [metaRows] = await connection.execute(
      'SELECT meta_key, meta_value FROM wp_usermeta WHERE user_id = ? AND meta_key IN (?, ?, ?, ?)',
      [user.ID, 'membership_level', 'vip_level', 'user_role', 'wp_capabilities']
    );
    
    // 处理用户元数据
    const userMeta = {};
    metaRows.forEach(row => {
      userMeta[row.meta_key] = row.meta_value;
    });
    
    // 解析WordPress角色
    let role = 'subscriber';
    let membershipLevel = 'free';
    let vipLevel = 0;
    
    if (userMeta.wp_capabilities) {
      try {
        const capabilities = JSON.parse(userMeta.wp_capabilities);
        if (capabilities.administrator) role = 'administrator';
        else if (capabilities.editor) role = 'editor';
        else if (capabilities.author) role = 'author';
        else if (capabilities.contributor) role = 'contributor';
      } catch (e) {
        // 如果解析失败，保持默认值
      }
    }
    
    if (userMeta.membership_level) {
      membershipLevel = userMeta.membership_level;
    }
    
    if (userMeta.vip_level) {
      vipLevel = parseInt(userMeta.vip_level) || 0;
    }
    
    connection.release();
    
    return {
      id: user.ID,
      username: user.user_login,
      email: user.user_email,
      display_name: user.user_nicename,
      role: role,
      membership_level: membershipLevel,
      vip_level: vipLevel
    };
  } catch (error) {
    console.error('WordPress用户验证失败:', error);
    return null;
  }
}

// 根据ID获取用户信息
async function getUserById(userId) {
  try {
    const connection = await pool.getConnection();
    
    const [userRows] = await connection.execute(
      'SELECT ID, user_login, user_email, user_nicename, user_status FROM wp_users WHERE ID = ? AND user_status = 0',
      [userId]
    );
    
    if (userRows.length === 0) {
      connection.release();
      return null;
    }
    
    const user = userRows[0];
    
    // 获取用户元数据
    const [metaRows] = await connection.execute(
      'SELECT meta_key, meta_value FROM wp_usermeta WHERE user_id = ? AND meta_key IN (?, ?, ?, ?)',
      [user.ID, 'membership_level', 'vip_level', 'user_role', 'wp_capabilities']
    );
    
    const userMeta = {};
    metaRows.forEach(row => {
      userMeta[row.meta_key] = row.meta_value;
    });
    
    let role = 'subscriber';
    let membershipLevel = 'free';
    let vipLevel = 0;
    
    if (userMeta.wp_capabilities) {
      try {
        const capabilities = JSON.parse(userMeta.wp_capabilities);
        if (capabilities.administrator) role = 'administrator';
        else if (capabilities.editor) role = 'editor';
        else if (capabilities.author) role = 'author';
        else if (capabilities.contributor) role = 'contributor';
      } catch (e) {
        // 如果解析失败，保持默认值
      }
    }
    
    if (userMeta.membership_level) {
      membershipLevel = userMeta.membership_level;
    }
    
    if (userMeta.vip_level) {
      vipLevel = parseInt(userMeta.vip_level) || 0;
    }
    
    connection.release();
    
    return {
      id: user.ID,
      username: user.user_login,
      email: user.user_email,
      display_name: user.user_nicename,
      role: role,
      membership_level: membershipLevel,
      vip_level: vipLevel
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
}

module.exports = {
  pool,
  testConnection,
  initializeTables,
  verifyWordPressPassword,
  getUserById
};
