const crypto = require('crypto');

// WordPress PHPass密码验证
function wpCheckPassword(password, hash) {
  // 如果是明文密码，直接比较
  if (!hash.startsWith('$P$') && !hash.startsWith('$H$')) {
    return password === hash;
  }

  // PHPass哈希验证
  const itoa64 = './0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  
  if (hash.length !== 34) {
    return false;
  }

  const count_log2 = itoa64.indexOf(hash[3]);
  if (count_log2 < 7 || count_log2 > 30) {
    return false;
  }

  const count = 1 << count_log2;
  const salt = hash.substring(4, 12);
  
  if (hash.length !== 34) {
    return false;
  }

  let hashResult = crypto.createHash('md5').update(salt + password).digest();
  
  for (let i = 0; i < count; i++) {
    hashResult = crypto.createHash('md5').update(hashResult + password).digest();
  }

  const output = hash.substring(0, 12);
  const encoded = encode64(hashResult, 16);
  
  return output + encoded === hash;
}

function encode64(input, count) {
  const itoa64 = './0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  let output = '';
  let i = 0;
  
  do {
    let value = input[i++];
    output += itoa64[value & 0x3f];
    
    if (i < count) {
      value |= input[i] << 8;
    }
    
    output += itoa64[(value >> 6) & 0x3f];
    
    if (i++ >= count) {
      break;
    }
    
    if (i < count) {
      value |= input[i] << 16;
    }
    
    output += itoa64[(value >> 12) & 0x3f];
    
    if (i++ >= count) {
      break;
    }
    
    output += itoa64[(value >> 18) & 0x3f];
  } while (i < count);
  
  return output;
}

module.exports = { wpCheckPassword };
