# MoonTV 项目优化步骤记录

## 当前状态
- 服务器地址: 47.111.120.88
- 项目路径: /www/wwwroot/moontv
- API服务路径: /www/wwwroot/moontv-api
- 当前功能: 用户名+邮箱登录验证已完成

## 用户提出的三个改进需求

### 1. 创建备份 ✅
- **问题**: 当前项目包含 node_modules (1GB+)，传输太慢
- **解决方案**: 直接在服务器上创建备份，避免大文件传输

### 2. 视频源优化
- **问题**: 视频源太少，可能是等待时间太短导致选择不够多
- **分析**: 
  - 当前API超时设置: 3-4秒
  - 缓存时间: 1800秒
  - 视频源数量: 20个（包含8个量子资源）
- **优化方向**:
  - 增加API超时时间到6-8秒
  - 添加更多视频源
  - 优化并发请求策略

### 3. UI细节完善
- **配色改进**: 黑绿配色 → 黑蓝配色
- **标题美化**: "电玩管家视频站" 居中和美化
- **其他细节**: 完善各处UI细节

## 具体优化步骤

### 步骤1: 服务器端备份
```bash
# 在服务器上创建备份
cd /www/wwwroot
cp -r moontv moontv-backup-v1
cp -r moontv-api moontv-api-backup-v1

# 清理备份中的 node_modules (节省空间)
rm -rf moontv-backup-v1/node_modules
rm -rf moontv-api-backup-v1/node_modules
```

### 步骤2: 视频源优化
```bash
# 修改配置文件
cd /www/wwwroot/moontv

# 1. 增加API超时时间
# 编辑 src/lib/api.ts 或相关配置文件
# 将超时从 3000ms 增加到 6000-8000ms

# 2. 添加更多视频源到 config.json
# 添加更多可靠的视频API源

# 3. 优化并发请求
# 修改视频搜索逻辑，允许更多并发请求
```

### 步骤3: UI配色优化
```bash
# 修改主题配色
# 1. 编辑 src/app/globals.css
# 将绿色主题色改为蓝色

# 2. 编辑组件样式文件
# 更新所有使用绿色的地方为蓝色

# 3. 优化标题样式
# 编辑 src/components/Header.tsx 或相关组件
# 改进 "电玩管家视频站" 的居中和美化
```

### 步骤4: 重新构建和部署
```bash
# 构建项目
npm run build

# 重启服务
pm2 restart moontv-frontend
pm2 restart moontv-api
```

## 文件修改清单

### 需要修改的主要文件:
1. `config.json` - 添加更多视频源
2. `src/lib/api.ts` - 增加超时时间
3. `src/app/globals.css` - 配色修改
4. `src/components/Header.tsx` - 标题美化
5. `tailwind.config.js` - 主题色配置

### 配色修改对照表:
- 主色调: `#10b981` (绿色) → `#3b82f6` (蓝色)
- 悬停色: `#059669` (深绿) → `#2563eb` (深蓝)
- 背景色: `#064e3b` (深绿背景) → `#1e3a8a` (深蓝背景)

## 预期效果
1. **备份完成**: 安全的项目版本保存
2. **视频源增加**: 更多视频选择，更快的搜索响应
3. **UI美化**: 更协调的蓝色主题，更美观的标题设计

## 下一步行动
直接在服务器上执行优化，避免大文件传输，提高效率。
