# WordPress会员认证系统壳

一个基于WordPress + RiPro主题的会员认证系统，支持用户名+邮箱验证和会员等级控制。

## 功能特性

- ✅ **WordPress用户验证** - 支持用户名+邮箱双重验证
- ✅ **会员等级控制** - 只允许高级会员访问
- ✅ **到期时间检查** - 自动验证会员是否过期
- ✅ **多种密码格式** - 支持bcrypt、PHPass、MD5、明文密码
- ✅ **JWT令牌认证** - 安全的会话管理
- ✅ **RESTful API** - 标准化接口设计
- ✅ **详细日志记录** - 便于调试和监控

## 系统架构

```
前端应用 → API服务 → WordPress数据库
```

## 快速开始

### 1. 环境要求

- Node.js 18+
- MySQL 5.7+
- WordPress + RiPro主题

### 2. 安装依赖

```bash
cd wordpress-auth-shell
npm install
```

### 3. 配置环境变量

复制 `.env.example` 到 `.env` 并修改配置：

```bash
cp .env.example .env
```

### 4. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm start

# PM2部署
npm run pm2:start
```

## 配置说明

### 数据库配置

在 `.env` 文件中配置WordPress数据库连接：

```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_db_name
```

### 会员字段配置

系统使用RiPro主题的会员字段：

- `cao_user_type` - 会员类型（normal/premium）
- `cao_vip_end_time` - 会员到期时间（YYYY-MM-DD）

## API接口

### 登录接口

```http
POST /api/login
Content-Type: application/json

{
  "username": "用户名",
  "email": "邮箱地址"
}
```

**成功响应：**
```json
{
  "success": true,
  "message": "登录成功",
  "token": "jwt_token_here",
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "displayName": "测试用户",
    "userType": "premium",
    "vipEndTime": "2025-08-26",
    "membershipStatus": {
      "status": "active",
      "message": "高级会员有效期至 2025-08-26",
      "canAccess": true
    }
  }
}
```

**失败响应：**
```json
{
  "success": false,
  "message": "需要高级会员权限才能访问",
  "membershipRequired": true
}
```

### 验证令牌

```http
POST /api/verify
Authorization: Bearer jwt_token_here
```

### 健康检查

```http
GET /api/health
```

## 集成指南

### 前端集成

```javascript
// 登录函数
async function login(username, email) {
  const response = await fetch('/api/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ username, email })
  });
  
  const data = await response.json();
  
  if (data.success) {
    localStorage.setItem('token', data.token);
    localStorage.setItem('user', JSON.stringify(data.user));
    return data;
  } else {
    throw new Error(data.message);
  }
}

// 验证令牌
async function verifyToken() {
  const token = localStorage.getItem('token');
  if (!token) return false;
  
  const response = await fetch('/api/verify', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return response.ok;
}
```

### 中间件保护

```javascript
// 保护需要会员权限的路由
app.use('/protected', async (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await getUserById(decoded.userId);
    
    if (!user || !user.membershipStatus.canAccess) {
      return res.status(403).json({
        success: false,
        message: '需要高级会员权限'
      });
    }
    
    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({
      success: false,
      message: '无效的令牌'
    });
  }
});
```

## 部署说明

### PM2部署

```bash
# 启动服务
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 查看日志
pm2 logs wordpress-auth

# 重启服务
pm2 restart wordpress-auth
```

### Docker部署

```bash
# 构建镜像
docker build -t wordpress-auth-shell .

# 运行容器
docker run -d \
  --name wordpress-auth \
  -p 3001:3001 \
  -e DB_HOST=your_db_host \
  -e DB_USER=your_db_user \
  -e DB_PASSWORD=your_db_password \
  -e DB_NAME=your_db_name \
  wordpress-auth-shell
```

## 自定义配置

### 修改会员验证逻辑

编辑 `src/auth/membership.js` 文件：

```javascript
function checkMembershipStatus(userType, vipEndTime) {
  // 自定义会员验证逻辑
  // 可以根据需要修改会员等级和到期时间的判断
}
```

### 添加新的API接口

在 `src/routes/` 目录下添加新的路由文件。

### 修改数据库字段

在 `src/database/` 目录下修改数据库查询逻辑。

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置是否正确
   - 确认数据库服务是否运行
   - 验证网络连接

2. **会员验证失败**
   - 检查WordPress用户表结构
   - 确认RiPro主题字段是否存在
   - 验证会员数据格式

3. **JWT令牌错误**
   - 检查JWT_SECRET环境变量
   - 确认令牌格式是否正确
   - 验证令牌是否过期

## 许可证

MIT License

## 支持

如有问题请提交Issue或联系开发者。
