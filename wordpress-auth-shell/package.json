{"name": "wordpress-auth-shell", "version": "1.0.0", "description": "WordPress会员认证系统壳 - 支持用户名+邮箱验证和会员等级控制", "main": "src/index.js", "scripts": {"start": "NODE_ENV=production node src/index.js", "dev": "NODE_ENV=development nodemon src/index.js", "test": "jest", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop wordpress-auth", "pm2:restart": "pm2 restart wordpress-auth", "pm2:delete": "pm2 delete wordpress-auth", "pm2:logs": "pm2 logs wordpress-auth", "docker:build": "docker build -t wordpress-auth-shell .", "docker:run": "docker run -d --name wordpress-auth -p 3001:3001 wordpress-auth-shell"}, "keywords": ["wordpress", "authentication", "membership", "ripro", "jwt", "mysql", "express", "api"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "express-rate-limit": "^6.8.1", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/wordpress-auth-shell.git"}, "bugs": {"url": "https://github.com/yourusername/wordpress-auth-shell/issues"}, "homepage": "https://github.com/yourusername/wordpress-auth-shell#readme"}