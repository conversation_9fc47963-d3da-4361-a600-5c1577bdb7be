const mysql = require('mysql2/promise');
const { logger } = require('../utils/logger');

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

let connection = null;

/**
 * 获取数据库连接
 * @returns {Promise<Connection>} 数据库连接
 */
async function getConnection() {
  if (!connection) {
    try {
      connection = await mysql.createConnection(dbConfig);
      logger.info('✅ 数据库连接成功');
    } catch (error) {
      logger.error('❌ 数据库连接失败:', error);
      throw error;
    }
  }
  return connection;
}

/**
 * 测试数据库连接
 * @returns {Promise<boolean>} 连接是否成功
 */
async function testDatabaseConnection() {
  try {
    const conn = await getConnection();
    await conn.execute('SELECT 1');
    return true;
  } catch (error) {
    logger.error('❌ 数据库连接测试失败:', error);
    throw error;
  }
}

/**
 * 关闭数据库连接
 */
async function closeConnection() {
  if (connection) {
    try {
      await connection.end();
      connection = null;
      logger.info('✅ 数据库连接已关闭');
    } catch (error) {
      logger.error('❌ 关闭数据库连接失败:', error);
    }
  }
}

module.exports = {
  getConnection,
  testDatabaseConnection,
  closeConnection
};
